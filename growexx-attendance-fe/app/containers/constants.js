// App-Routes
export const ROUTES = {
  HOME: '/',
  CONNECTED_JIRA: '/connected-jira',
  LOGIN: '/login',
  FORGOT_PASSWORD: '/forgot-password',
  LOGS: '/logs',
  REVIEW_LOGS: '/review-logs',
  USERS: '/users',
  LOGOUT: '/logout',
  UNAUTHORIZED: '/403',
  CHANGE_PASSWORD: '/change-password',
  PROJECTS: '/projects',
  RESET_PASSWORD: '/reset-password/:id',
  ASSIGNED_KRA: '/assigned-kra',
  STOCK_KRA: '/stock-kra',
  EDIT_KRA: '/edit-kra',
  ASSESSMENT: '/assessment',
  SELF_ASSESSMENT: '/assessment/self-comment',
  PM_ASSESSMENT: '/assessment/reporting-manager',
  RM_ASSESSMENT: '/assessment/review-manager',
  VIEW_KRA: '/assessment/view/:id',
  VIEW_KRA_RM: '/assessment/rm-view/:id',
  RATE_KRA: '/assessment/rate/:id',
  ASSIGN_KRA_DETAIL: '/assign-user-kra/detail', // userId/:designation/:year/:quarter
  ASSIGN_KRA: '/assign-kra',
  START_ASSESSMENT: '/start-assessment',
  SELF_KRA: '/self-kra',
  SELF_VIEW_KRA: '/self-kra/view/:id',
  SELF_RATE_KRA: '/self-kra/rate/:id',
  CATEGORY_WEIGHTAGE_MASTER: '/category-weightage-master',
  ASSIGN_BULK_KRA: '/bulk-kra-assignment',
  PROJECT_TRACKER: '/project-tracker',
  RAG_REPORT: '/rag-report',
  BULk_ACTION: '/bulk-action',
  EMPLOYEE_PROFILE: '/pli/employee-profile/:employeeId',
  // EMPLOYEE_PROFILE_SUBMIT: '/pli/employee-profile/:employeeId/submit',
  // EMPLOYEE_PROFILE_QUERY: '/pli/employee-profile/:employeeId/raise-query',
  PLI: '/mentee',
  PARAMETER_CONFIGURATION: '/parameter-configuration',
  PLI_RATINGS: '/pli-ratings-overview',
  MY_PLI_RATING: '/my-pli-rating',
};

export const PRODUCT_NAME = 'Growexx';

// API-ROUTES
export const API_URL = process.env.ATTENDANCE_API_URL;

export const AUTH = 'auth';
export const JIRA = 'jira';
export const USERS = 'user';
export const KRA = 'kra';
export const PROJECT_TRACKER = 'project-tracker';
export const ASSESSMENT = 'assessment';
export const PROJECTS = 'project';
export const RAG = 'rag';
export const API_ENDPOINTS = {
  // auth
  LOGIN: `${API_URL}/${AUTH}/signin`,
  FORGOT_PASSWORD: `${API_URL}/${AUTH}/forgot-password`,
  RESET_PASSWORD: `${API_URL}/${AUTH}/reset-password`,
  VERIFY_TOKEN: `${API_URL}/${AUTH}/verify-token`,

  // jira
  CONNECT_JIRA: `${API_URL}/${JIRA}/portal`,
  GET_CONNECTED_JIRA: `${API_URL}/${JIRA}/portals`,
  PATCH_CONNECTED_JIRA: `${API_URL}/${JIRA}/portal/status`,
  SYNC_LOGS: `${API_URL}/${JIRA}/logs`,

  // user
  USER_DETAILS_API: `${API_URL}/${USERS}/details`,
  USER_ATTENDANCE: `${API_URL}/${USERS}/attendance`,
  USER_LIST: `${API_URL}/${USERS}`,
  UPDATE_USER: `${API_URL}/${USERS}`,
  UPDATE_PASSWORD: `${API_URL}/${USERS}/password`,
  LOGS_DOWNLOAD: `${API_URL}/${USERS}/attendance/download`,
  ADD_DESIGNATION: `${API_URL}/${USERS}/designation`,
  PERSON_DAT_LOGS_DOWNLOAD: `${API_URL}/${USERS}/person-day/download`,
  ATTENDANCE_LOGS_DOWNLOAD: `${API_URL}/${USERS}/logs/download`,
  BILLING_SHEET_DOWNLOAD: `${API_URL}/${USERS}/billing-sheet/download`,
  ADD_PM: `${API_URL}/${USERS}/role`,
  EMPLOYEES_LIST: `${API_URL}/${USERS}/members`,
  PROJECT_MANAGERS_LIST: `${API_URL}/${USERS}/pms`,
  USERS: `${API_URL}/${USERS}`,
  USER_ACTIVE_INACTIVE: `${API_URL}/${USERS}/status`,
  BU_LIST: `${API_URL}/${USERS}/bu`,
  ATTENDANCE_BY_PROJECT: `${API_URL}/${USERS}/attendance-by-project`,
  UPDATE_LOG_STATUS: `${API_URL}/${USERS}/logs/update-status`,
  GET_SERVER_TIME: `${API_URL}/${USERS}/server-time`,

  // Mentee
  GET_MENTEE: `${API_URL}/${USERS}/mentees`,

  // project
  ALL_PROJECTS_LIST: `${API_URL}/${PROJECTS}/all`,
  PROJECT_LIST: `${API_URL}/${PROJECTS}`,
  UPDATE_PROJECT: `${API_URL}/${PROJECTS}`,
  ACTIVE_PROJECT_LIST: `${API_URL}/${PROJECTS}/all/logs`,
  PROJECT_DOWNLOAD: `${API_URL}/${PROJECTS}/person-day/download`,
  PROJECT_HEADER_RESET: `${API_URL}/${PROJECTS}/person-day/reset-headers`,
  UPDATE_PROJECT_STATE: `${API_URL}/${PROJECTS}/status`,
  REVIEW_LOGS_PROJECTS_LIST: `${API_URL}/${PROJECTS}/all-by-log-count`,

  // leave
  UPLOAD_USERS_LEAVE: `${API_URL}/${USERS}/leaves`,
  ADD_USERS_LEAVE: `${API_URL}/${USERS}/leave`,

  // KRA
  KRA_LIST: `${API_URL}/${KRA}/all`,
  ASSIGN_KRA_DETAIL: `${API_URL}/${KRA}/list/view/assign`,
  KRA_LIST_GROUP_BY_DESIGNATION: `${API_URL}/${KRA}/list/group-by-designation`,
  DELETE_KRA_LIST: `${API_URL}/${KRA}`,
  IMPORT_KRA: `${API_URL}/${KRA}/import`,
  KRA_ASSESSMENT: `${API_URL}/${KRA}/assessment`,
  ASSIGN_KRA: `${API_URL}/${KRA}/assign`,
  UPDATE_KRA: `${API_URL}/${KRA}`,
  ASSIGNED_KRA_LIST: `${API_URL}/${KRA}/assigned-to-users/list`,
  KRA_RELEASE_RATINGS: `${API_URL}/${KRA}/release-ratings`,
  KRA_CATEGORY_WEIGHTAGE: `${API_URL}/${KRA}/category-weightage`,
  WEIGHTAGE_LIST: `${API_URL}/${KRA}/list/category-weightage`,
  FEEDBACK_MEETING_START: `${API_URL}/${KRA}/assessment/feedback-meeting`,
  KRA_DOWNLOAD: `${API_URL}/${KRA}/download-kra-data`,
  REPORT_DOWNLOAD: `${API_URL}/${KRA}/download`,
  KRA_STATUS_DOWNLOAD: `${API_URL}/${KRA}/download/kra-status`,
  ASSIG_BULK_KRA: `${API_URL}/${KRA}/bulk-assign`,
  BU_HEAD_APPROVED: `${API_URL}/${KRA}/bu-head-approved`,

  // SELF KRA
  SELF_KRA: `${API_URL}/${KRA}/self`,

  // SAVE & FREEZE RATING
  SAVE_REPORTING_MANAGER: `${API_URL}/${KRA}/rate/reporting/save`,
  SAVE_REVIEW_MANAGER: `${API_URL}/${KRA}/rate/reviewer/save`,
  SAVE_USER: `${API_URL}/${KRA}/rate/self/save`,
  FREEZE_REPORTING_MANAGER: `${API_URL}/${KRA}/rate/reporting/freeze`,
  FREEZE_REVIEW_MANAGER: `${API_URL}/${KRA}/rate/reviewer/freeze`,
  UNFREEZE_REVIEW_MANAGER: `${API_URL}/${KRA}/user-kra/freezed-by-review-manager`,
  FREEZE_USER: `${API_URL}/${KRA}/rate/self/freeze`,
  KRA_PUBLISH: `${API_URL}/${KRA}/publish`,
  DOWNLOAD_KRA_ATTACHMENT: `${API_URL}/${KRA}/download`,
  UPLOAD_KRA_ATTACHMENT: `${API_URL}/${KRA}/upload`,
  KRA_NOTIFICATION: `${API_URL}/${KRA}/notification`,
  FEEDBACK_USER: `${API_URL}/${KRA}/user-kra/feedback-meeting-date`,

  // PROJECT TRACKER
  GET_CARDS: `${API_URL}/${PROJECT_TRACKER}/health-cards`,
  GET_ALL_PROJECTS: `${API_URL}/${PROJECT_TRACKER}/project/all`,
  GET_PROJECT_TRACKER: `${API_URL}/${PROJECT_TRACKER}/project`,
  GET_SPRINT_REPORT: `${API_URL}/${PROJECT_TRACKER}/sprint`,
  DOWNLOAD_REPORTS: `${API_URL}/${PROJECT_TRACKER}/download-report`,
  GET_MONTHLY_DEVIATION: `${API_URL}/${PROJECT_TRACKER}/monthly-deviation`,

  // RAG REPORT
  RAG_REPORT: `${API_URL}/${RAG}`,
  SPRINT_METRICS_REPORT: `${API_URL}/${RAG}/sprint-metrics`,
  TRIGGER_CRON: `${API_URL}/${PROJECT_TRACKER}/trigger-cron`,
  TRIGGER_RAG_CRON: `${API_URL}/${RAG}/trigger-cron`,
  CRON_STATUS: `${API_URL}/${PROJECT_TRACKER}/cron-status`,
  DOWNLOAD_RAG_REPORT: `${API_URL}/${RAG}/download-report`,
  FREEZE_RAG: `${API_URL}/${RAG}/freeze-report`,

  // PLI Parameters
  SAVE_PARAMETER_CONFIG: `${API_URL}/api/pli-parameters`,
  GET_PARAMETER_CONFIG: `${API_URL}/api/pli-parameters`,

  // PLI Ratings
  PLI_RATINGS: `${API_URL}/api/pli-ratings`,
  PLI_RATING_DETAIL: `${API_URL}/api/pli-ratings/:id`,
  PLI_RATING_FINALIZE: `${API_URL}/api/pli-ratings/:id/finalize`,
  PLI_RATING_QUERY: `${API_URL}/api/pli-ratings/:id/query`,
  ALL_EMPLOYEE_PLI: `${API_URL}/pli/all-employee-profile`,

  // Mentor-Mentee Operations
  PROCESS_BULK_ASSIGNMENTS: `${API_URL}/user/mentees/process-assignments`,
  FETCH_MENTOR_MENTEE_DATA: `${API_URL}/user/mentees/grouped-by-mentor`,
};

export const ATTACHMENT_MAX_SIZE = 1048576;
export const ATTACHMENT_MIN_SIZE = 5120;

// Table Pagination default
export const DEFAULT_PAGE = 1;
export const DEFAULT_LIMIT = 10;

export const SORTING = {
  ASC: 1,
  DESC: -1,
  ascend: 1,
  descend: -1,
};

/**
 * Roles for system
 */
export const ROLES = {
  USER: 1,
  PM: 2,
  RM: 3,
  HR: 4,
  BU: 5,
};

/**
 *
 * @param {string} order  ['ascend','descend']
 * @returns
 */
export const GET_SORT_ORDER = (order, defaultSort = SORTING.DESC) => {
  switch (order) {
    case 'ascend':
      return SORTING.ASC;
    case 'descend':
      return SORTING.DESC;
    default:
      return defaultSort;
  }
};

export const DEFAULT_PAGE_NO = 1;
export const DEFAULT_SIZE = 10;
export const GET_DEFAULT_PAGINATION = (pageSize = DEFAULT_SIZE) => ({
  pageSize,
  current: DEFAULT_PAGE_NO,
  total: 0,
});

export const GENERIC_MOMENT_DATE_FORMAT = 'YYYY-MM-DD';

export const WEEKEND = [0, 6];

export const RESTRICTED_ROUTES = [
  ROUTES.CONNECTED_JIRA,
  ROUTES.START_ASSESSMENT,
  ROUTES.PROJECT_TRACKER,
  ROUTES.RAG_REPORT,
];

export const ROLE_BASED_ROUTE_ACCESS = {
  [ROLES.BU]: [
    ROUTES.CONNECTED_JIRA,
    ROUTES.START_ASSESSMENT,
    ROUTES.PROJECT_TRACKER,
    ROUTES.RAG_REPORT,
  ],
  [ROLES.HR]: [
    ROUTES.CONNECTED_JIRA,
    ROUTES.START_ASSESSMENT,
    ROUTES.PROJECT_TRACKER,
    ROUTES.RAG_REPORT,
  ],
  [ROLES.USER]: [],
  [ROLES.PM]: [ROUTES.PROJECT_TRACKER, ROUTES.RAG_REPORT],
  [ROLES.RM]: [ROUTES.PROJECT_TRACKER, ROUTES.RAG_REPORT],
};

export const ROLE_BASED_DEFAULT_ROUTE = {
  [ROLES.BU]: ROUTES.CONNECTED_JIRA,
  [ROLES.HR]: ROUTES.CONNECTED_JIRA,
  [ROLES.USER]: ROUTES.LOGS,
  [ROLES.PM]: ROUTES.LOGS,
  [ROLES.RM]: ROUTES.LOGS,
};

export const ROLE_BASED_SIDEBAR_MENU = {
  [ROLES.BU]: [
    ROUTES.CONNECTED_JIRA,
    ROUTES.USERS,
    ROUTES.PROJECTS,
    ROUTES.ASSIGNED_KRA,
    ROUTES.STOCK_KRA,
    ROUTES.ASSESSMENT,
    ROUTES.PM_ASSESSMENT,
    ROUTES.RM_ASSESSMENT,
    ROUTES.ASSIGN_KRA_DETAIL,
    ROUTES.CATEGORY_WEIGHTAGE_MASTER,
    ROUTES.PROJECT_TRACKER,
    ROUTES.RAG_REPORT,
    ROUTES.PLI,
    ROUTES.PARAMETER_CONFIGURATION,
  ],
  [ROLES.HR]: [
    ROUTES.CONNECTED_JIRA,
    ROUTES.USERS,
    ROUTES.PROJECTS,
    ROUTES.ASSIGNED_KRA,
    ROUTES.STOCK_KRA,
    ROUTES.ASSESSMENT,
    ROUTES.PM_ASSESSMENT,
    ROUTES.RM_ASSESSMENT,
    ROUTES.ASSIGN_KRA_DETAIL,
    ROUTES.CATEGORY_WEIGHTAGE_MASTER,
    ROUTES.PROJECT_TRACKER,
    ROUTES.RAG_REPORT,
    ROUTES.PLI,
    ROUTES.PARAMETER_CONFIGURATION,
  ],
  [ROLES.USER]: [
    ROUTES.LOGS,
    ROUTES.PROJECTS,
    ROUTES.ASSESSMENT,
    ROUTES.PM_ASSESSMENT,
    ROUTES.RM_ASSESSMENT,
    ROUTES.PLI,
  ],
  [ROLES.PM]: [
    ROUTES.LOGS,
    ROUTES.PROJECTS,
    ROUTES.STOCK_KRA,
    ROUTES.ASSESSMENT,
    ROUTES.PM_ASSESSMENT,
    ROUTES.RM_ASSESSMENT,
    ROUTES.PROJECT_TRACKER,
    ROUTES.RAG_REPORT,
    ROUTES.PLI,
  ],
  [ROLES.RM]: [
    ROUTES.LOGS,
    ROUTES.PROJECTS,
    ROUTES.STOCK_KRA,
    ROUTES.ASSESSMENT,
    ROUTES.PM_ASSESSMENT,
    ROUTES.RM_ASSESSMENT,
    ROUTES.PROJECT_TRACKER,
    ROUTES.RAG_REPORT,
    ROUTES.PLI,
  ],
};

export const EMPLOYEE_LEVEL_LIST = [
  {
    label: 'L0',
    value: 'L0',
  },
  {
    label: 'L1',
    value: 'L1',
  },
  {
    label: 'L2',
    value: 'L2',
  },
  {
    label: 'L3',
    value: 'L3',
  },
  {
    label: 'L4',
    value: 'L4',
  },
  {
    label: 'L5',
    value: 'L5',
  },
  {
    label: 'L6',
    value: 'L6',
  },
];

export const BUSINESS_UNIT_LIST = [
  {
    label: 'Data Science',
    value: 'Data Science',
  },
  {
    label: 'HR & Operations',
    value: 'HR & Operations',
  },
  {
    label: 'Management',
    value: 'Management',
  },
  {
    label: 'Microsoft Services',
    value: 'Microsoft Services',
  },
  {
    label: 'Sales',
    value: 'Sales',
  },
  {
    label: 'Web & Mobile',
    value: 'Web & Mobile',
  },
  {
    label: 'Oracle',
    value: 'Oracle',
  },
  {
    label: 'N/A',
    value: 'N/A',
  },
];

export const DESIGNATION_LIST = [
  {
    label: 'Asst. Manager - HR',
    value: 'Asst. Manager - HR',
  },
  {
    label: 'Asst. Manager - Talent Acquisition',
    value: 'Asst. Manager - Talent Acquisition',
  },
  {
    label: 'Business Advisor',
    value: 'Business Advisor',
  },
  {
    label: 'Business Analyst',
    value: 'Business Analyst',
  },
  {
    label: 'Business Development Executive',
    value: 'Business Development Executive',
  },
  {
    label: 'Business Development Manager',
    value: 'Business Development Manager',
  },
  {
    label: 'Business Unit Head - Web & Mobile',
    value: 'Business Unit Head - Web & Mobile',
  },
  {
    label: 'Co-Founder',
    value: 'Co-Founder',
  },
  {
    label: 'Data Analyst',
    value: 'Data Analyst',
  },
  {
    label: 'Database Administrator',
    value: 'Database Administrator',
  },
  {
    label: 'Data Engineer',
    value: 'Data Engineer',
  },
  {
    label: 'Data Scientist',
    value: 'Data Scientist',
  },
  {
    label: 'Digital Marketing Head',
    value: 'Digital Marketing Head',
  },
  {
    label: 'Director',
    value: 'Director',
  },
  {
    label: 'Engineer - IT & DevOps',
    value: 'Engineer - IT & DevOps',
  },
  {
    label: 'Executive - Business Development',
    value: 'Executive - Business Development',
  },
  {
    label: 'Executive - Digital Marketing',
    value: 'Executive - Digital Marketing',
  },
  {
    label: 'Executive - HR',
    value: 'Executive - HR',
  },
  {
    label: 'Executive - Lead Generation',
    value: 'Executive - Lead Generation',
  },
  {
    label: 'Executive - Talent Acquisition',
    value: 'Executive - Talent Acquisition',
  },
  {
    label: 'Executive - Web Research',
    value: 'Executive - Web Research',
  },
  {
    label: 'Executive Assistant',
    value: 'Executive Assistant',
  },
  {
    label: 'Head - HR & Ops',
    value: 'Head - HR & Ops',
  },
  {
    label: 'Jr. Data Analyst',
    value: 'Jr. Data Analyst',
  },
  {
    label: 'Jr. Database Administrator',
    value: 'Jr. Database Administrator',
  },
  {
    label: 'Jr. Data Engineer',
    value: 'Jr. Data Engineer',
  },
  {
    label: 'Jr. Software Engineer',
    value: 'Jr. Software Engineer',
  },
  {
    label: 'Jr. Software Engineer - DevOps',
    value: 'Jr. Software Engineer - DevOps',
  },
  {
    label: 'Lead - Data Engineering',
    value: 'Lead - Data Engineering',
  },
  {
    label: 'Lead Engineer - QA',
    value: 'Lead Engineer - QA',
  },
  {
    label: 'Lead - UX',
    value: 'Lead - UX',
  },
  {
    label: 'Management Trainee',
    value: 'Management Trainee',
  },
  {
    label: 'Management Trainee - HR',
    value: 'Management Trainee - HR',
  },
  {
    label: 'Manager - Business Development',
    value: 'Manager - Business Development',
  },
  {
    label: 'Manager - Data Engineering',
    value: 'Manager - Data Engineering',
  },
  {
    label: 'Manager - HR',
    value: 'Manager - HR',
  },
  {
    label: 'Manager Solutions',
    value: 'Manager Solutions',
  },
  {
    label: 'Office Boy',
    value: 'Office Boy',
  },
  {
    label: 'Practice Head - Data Science and Analytics',
    value: 'Practice Head - Data Science and Analytics',
  },
  {
    label: 'Product Manager',
    value: 'Product Manager',
  },
  {
    label: 'Technical Consultant',
    value: 'Technical Consultant',
  },
  {
    label: 'Product Owner',
    value: 'Product Owner',
  },
  {
    label: 'Project Manager',
    value: 'Project Manager',
  },
  {
    label: 'Regional Head - Sales',
    value: 'Regional Head - Sales',
  },
  {
    label: 'Scrum Master',
    value: 'Scrum Master',
  },
  {
    label: 'SEO Executive',
    value: 'SEO Executive',
  },
  {
    label: 'Software Engineer',
    value: 'Software Engineer',
  },
  {
    label: 'Software Engineer - DevOps',
    value: 'Software Engineer - DevOps',
  },
  {
    label: 'Software Engineer - QA',
    value: 'Software Engineer - QA',
  },
  {
    label: 'Sr. Business Analyst',
    value: 'Sr. Business Analyst',
  },
  {
    label: 'Sr. Data Analyst',
    value: 'Sr. Data Analyst',
  },
  {
    label: 'Sr. Database Administrator',
    value: 'Sr. Database Administrator',
  },
  {
    label: 'Sr. Data Engineer',
    value: 'Sr. Data Engineer',
  },
  {
    label: 'Sr. Data Scientist',
    value: 'Sr. Data Scientist',
  },
  {
    label: 'Sr. Executive - Business Development',
    value: 'Sr. Executive - Business Development',
  },
  {
    label: 'Sr. Executive - Digital Marketing',
    value: 'Sr. Executive - Digital Marketing',
  },
  {
    label: 'Sr. Executive - HR',
    value: 'Sr. Executive - HR',
  },
  {
    label: 'Sr. Executive - Lead Generation',
    value: 'Sr. Executive - Lead Generation',
  },
  {
    label: 'Sr. Executive - Talent Acquisition',
    value: 'Sr. Executive - Talent Acquisition',
  },
  {
    label: 'Sr. Executive - Web Research',
    value: 'Sr. Executive - Web Research',
  },
  {
    label: 'Sr. Product Owner',
    value: 'Sr. Product Owner',
  },
  {
    label: 'Sr. Software Engineer',
    value: 'Sr. Software Engineer',
  },
  {
    label: 'Sr. Software Engineer - DevOps',
    value: 'Sr. Software Engineer - DevOps',
  },
  {
    label: 'Sr. Software Engineer - QA',
    value: 'Sr. Software Engineer - QA',
  },
  {
    label: 'Sr. Technical Lead',
    value: 'Sr. Technical Lead',
  },
  {
    label: 'Sr. Technical Project Manager',
    value: 'Sr. Technical Project Manager',
  },
  {
    label: 'Sr. UI/UX Designer',
    value: 'Sr. UI/UX Designer',
  },
  {
    label: 'Technical Lead',
    value: 'Technical Lead',
  },
  {
    label: 'Sr. Web Analytics Engineer',
    value: 'Sr. Web Analytics Engineer',
  },
  {
    label: 'Technical Lead - DevOps',
    value: 'Technical Lead - DevOps',
  },
  {
    label: 'Technical Project Manager',
    value: 'Technical Project Manager',
  },
  {
    label: 'UI/UX Designer',
    value: 'UI/UX Designer',
  },
  {
    label: 'Web Research Executive',
    value: 'Web Research Executive',
  },
  {
    label: 'Service Delivery Manager',
    value: 'Service Delivery Manager',
  },
  {
    label: 'Jr. Consultant',
    value: 'Jr. Consultant',
  },
  {
    label: 'Sr. Consultant',
    value: 'Sr. Consultant',
  },
  {
    label: 'Data Science - Senior MLOps Engineer',
    value: 'Data Science - Senior MLOps Engineer',
  },
];
export const QUARTER_LIST = [
  {
    label: 'Q1',
    value: 'Q1',
  },
  {
    label: 'Q2',
    value: 'Q2',
  },
  {
    label: 'Q3',
    value: 'Q3',
  },
  {
    label: 'Q4',
    value: 'Q4',
  },
];
export const KRA_YEAR_LIST = [
  {
    label: '2022',
    value: 2022,
  },
  {
    label: '2023',
    value: 2023,
  },
  {
    label: '2024',
    value: 2024,
  },
  {
    label: '2025',
    value: 2025,
  },
];
export const PENDING_LIST = [
  {
    label: 'All',
    value: false,
  },
  {
    label: 'Pending',
    value: true,
  },
];
export const STATUS_LIST = [
  {
    label: 'KRA Saved',
    value: 'KRA Saved',
  },
  {
    label: 'KRA Assigned',
    value: 'KRA Assigned',
  },
  {
    label: 'Assessment Started',
    value: 'Assessment Started',
  },
  {
    label: 'Self Comment Frozen',
    value: 'Self Comment Frozen',
  },
  {
    label: 'Reporting Manager rating frozen',
    value: 'Reporting Manager rating frozen',
  },
  {
    label: 'Unfreezed for Reporting Manager',
    value: 'Unfreezed for Reporting Manager',
  },
  {
    label: 'Review Manager rating frozen',
    value: 'Review Manager rating frozen',
  },
  {
    label: 'Unfreezed for Review Manager',
    value: 'Unfreezed for Review Manager',
  },
  {
    label: 'BU Head Approved',
    value: 'BU Head Approved',
  },
  {
    label: 'Feedback Meeting Started',
    value: 'Feedback Meeting Started',
  },
  {
    label: 'Feedback meeting acknowledged',
    value: 'Feedback meeting acknowledged',
  },
  {
    label: 'Rating Released',
    value: 'Rating Released',
  },
];
export const KRA_CATEGORY_LIST = [
  {
    label: 'Business Result',
    value: 'Business Result',
  },
  {
    label: 'Development Goals',
    value: 'Development Goals',
  },
  {
    label: 'Impact to customer',
    value: 'Impact to customer',
  },
  {
    label: 'Process Adherence',
    value: 'Process Adherence',
  },
  {
    label: 'Value Add',
    value: 'Value Add',
  },
];

export const SUPER_ADMIN_LIST = ['<EMAIL>', '<EMAIL>'];

export const ragDropDownColumns = [
  'techAudit',
  'processAudit',
  // 'deliveryHeadComments',
  'clientEscalations',
  'apiCreated',
  'memberClientEscalation',
];
export const monthNames = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
];
